#!/usr/bin/env python3
"""
Advanced Code Cleaner and Optimizer
===================================

Comprehensive tool to analyze, clean, and optimize the agent.py file by:
1. Detecting and removing duplicate classes, functions, and code blocks
2. Optimizing imports and removing unused ones
3. Standardizing code formatting and structure
4. Upgrading features to modern Python standards

Author: AI Coding Agent
Version: 1.0.0
"""

import ast
import re
import os
import sys
import hashlib
import logging
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict, Counter
from dataclasses import dataclass
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CodeBlock:
    """Represents a code block for duplicate detection"""
    content: str
    start_line: int
    end_line: int
    block_type: str  # 'class', 'function', 'method', 'import', 'other'
    name: str = ""
    hash_value: str = ""
    
    def __post_init__(self):
        self.hash_value = hashlib.md5(self.content.encode()).hexdigest()

@dataclass
class DuplicateGroup:
    """Group of duplicate code blocks"""
    blocks: List[CodeBlock]
    duplicate_type: str
    confidence: float
    
class CodeAnalyzer:
    """Advanced code analyzer for duplicate detection"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.content = ""
        self.lines = []
        self.ast_tree = None
        self.classes = {}
        self.functions = {}
        self.imports = {}
        self.duplicates = []
        
    def load_file(self) -> bool:
        """Load and parse the file"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            
            self.lines = self.content.split('\n')
            
            # Parse AST
            try:
                self.ast_tree = ast.parse(self.content)
                return True
            except SyntaxError as e:
                logger.error(f"Syntax error in file: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Error loading file: {e}")
            return False
    
    def analyze_structure(self) -> Dict[str, Any]:
        """Analyze code structure and identify components"""
        if not self.ast_tree:
            return {}
            
        analyzer = StructureAnalyzer()
        analyzer.visit(self.ast_tree)
        
        self.classes = analyzer.classes
        self.functions = analyzer.functions
        self.imports = analyzer.imports
        
        return {
            'classes': len(self.classes),
            'functions': len(self.functions),
            'imports': len(self.imports),
            'total_lines': len(self.lines)
        }
    
    def find_duplicates(self) -> List[DuplicateGroup]:
        """Find all types of duplicates"""
        duplicates = []
        
        # Find duplicate classes
        class_duplicates = self._find_duplicate_classes()
        duplicates.extend(class_duplicates)
        
        # Find duplicate functions
        function_duplicates = self._find_duplicate_functions()
        duplicates.extend(function_duplicates)
        
        # Find duplicate imports
        import_duplicates = self._find_duplicate_imports()
        duplicates.extend(import_duplicates)
        
        # Find duplicate code blocks
        block_duplicates = self._find_duplicate_blocks()
        duplicates.extend(block_duplicates)
        
        self.duplicates = duplicates
        return duplicates
    
    def _find_duplicate_classes(self) -> List[DuplicateGroup]:
        """Find duplicate class definitions"""
        duplicates = []
        class_groups = defaultdict(list)
        
        for class_info in self.classes.values():
            # Group by class name
            class_groups[class_info['name']].append(class_info)
        
        for class_name, class_list in class_groups.items():
            if len(class_list) > 1:
                # Check if they're actually duplicates (similar content)
                similar_classes = self._group_similar_classes(class_list)
                for group in similar_classes:
                    if len(group) > 1:
                        blocks = [CodeBlock(
                            content=cls['content'],
                            start_line=cls['start_line'],
                            end_line=cls['end_line'],
                            block_type='class',
                            name=cls['name']
                        ) for cls in group]
                        
                        duplicates.append(DuplicateGroup(
                            blocks=blocks,
                            duplicate_type='class',
                            confidence=self._calculate_similarity_confidence(group)
                        ))
        
        return duplicates
    
    def _find_duplicate_functions(self) -> List[DuplicateGroup]:
        """Find duplicate function definitions"""
        duplicates = []
        function_groups = defaultdict(list)
        
        for func_info in self.functions.values():
            # Group by function name and signature
            key = f"{func_info['name']}_{func_info.get('signature', '')}"
            function_groups[key].append(func_info)
        
        for func_key, func_list in function_groups.items():
            if len(func_list) > 1:
                similar_functions = self._group_similar_functions(func_list)
                for group in similar_functions:
                    if len(group) > 1:
                        blocks = [CodeBlock(
                            content=func['content'],
                            start_line=func['start_line'],
                            end_line=func['end_line'],
                            block_type='function',
                            name=func['name']
                        ) for func in group]
                        
                        duplicates.append(DuplicateGroup(
                            blocks=blocks,
                            duplicate_type='function',
                            confidence=self._calculate_similarity_confidence(group)
                        ))
        
        return duplicates
    
    def _find_duplicate_imports(self) -> List[DuplicateGroup]:
        """Find duplicate import statements"""
        duplicates = []
        import_groups = defaultdict(list)
        
        for imp_info in self.imports.values():
            import_groups[imp_info['module']].append(imp_info)
        
        for module, import_list in import_groups.items():
            if len(import_list) > 1:
                blocks = [CodeBlock(
                    content=imp['content'],
                    start_line=imp['line'],
                    end_line=imp['line'],
                    block_type='import',
                    name=imp['module']
                ) for imp in import_list]
                
                duplicates.append(DuplicateGroup(
                    blocks=blocks,
                    duplicate_type='import',
                    confidence=1.0
                ))
        
        return duplicates
    
    def _find_duplicate_blocks(self) -> List[DuplicateGroup]:
        """Find duplicate code blocks using content similarity"""
        duplicates = []
        
        # Extract code blocks (functions, classes, etc.)
        blocks = []
        
        # Add all classes and functions as blocks
        for class_info in self.classes.values():
            blocks.append(CodeBlock(
                content=class_info['content'],
                start_line=class_info['start_line'],
                end_line=class_info['end_line'],
                block_type='class',
                name=class_info['name']
            ))
        
        for func_info in self.functions.values():
            blocks.append(CodeBlock(
                content=func_info['content'],
                start_line=func_info['start_line'],
                end_line=func_info['end_line'],
                block_type='function',
                name=func_info['name']
            ))
        
        # Group by content hash
        hash_groups = defaultdict(list)
        for block in blocks:
            hash_groups[block.hash_value].append(block)
        
        # Find groups with multiple blocks (duplicates)
        for hash_value, block_list in hash_groups.items():
            if len(block_list) > 1:
                duplicates.append(DuplicateGroup(
                    blocks=block_list,
                    duplicate_type='content',
                    confidence=1.0
                ))
        
        return duplicates
    
    def _group_similar_classes(self, classes: List[Dict]) -> List[List[Dict]]:
        """Group similar classes together"""
        groups = []
        processed = set()
        
        for i, cls1 in enumerate(classes):
            if i in processed:
                continue
                
            group = [cls1]
            processed.add(i)
            
            for j, cls2 in enumerate(classes[i+1:], i+1):
                if j in processed:
                    continue
                    
                similarity = self._calculate_class_similarity(cls1, cls2)
                if similarity > 0.8:  # 80% similarity threshold
                    group.append(cls2)
                    processed.add(j)
            
            groups.append(group)
        
        return groups
    
    def _group_similar_functions(self, functions: List[Dict]) -> List[List[Dict]]:
        """Group similar functions together"""
        groups = []
        processed = set()
        
        for i, func1 in enumerate(functions):
            if i in processed:
                continue
                
            group = [func1]
            processed.add(i)
            
            for j, func2 in enumerate(functions[i+1:], i+1):
                if j in processed:
                    continue
                    
                similarity = self._calculate_function_similarity(func1, func2)
                if similarity > 0.8:  # 80% similarity threshold
                    group.append(func2)
                    processed.add(j)
            
            groups.append(group)
        
        return groups
    
    def _calculate_class_similarity(self, cls1: Dict, cls2: Dict) -> float:
        """Calculate similarity between two classes"""
        # Compare method names, attributes, etc.
        content1 = cls1.get('content', '')
        content2 = cls2.get('content', '')
        
        return self._calculate_content_similarity(content1, content2)
    
    def _calculate_function_similarity(self, func1: Dict, func2: Dict) -> float:
        """Calculate similarity between two functions"""
        content1 = func1.get('content', '')
        content2 = func2.get('content', '')
        
        return self._calculate_content_similarity(content1, content2)
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """Calculate content similarity using various metrics"""
        if content1 == content2:
            return 1.0
        
        # Normalize content (remove whitespace, comments)
        norm1 = self._normalize_content(content1)
        norm2 = self._normalize_content(content2)
        
        if norm1 == norm2:
            return 0.95
        
        # Calculate Jaccard similarity
        words1 = set(norm1.split())
        words2 = set(norm2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _normalize_content(self, content: str) -> str:
        """Normalize content for comparison"""
        # Remove comments
        content = re.sub(r'#.*$', '', content, flags=re.MULTILINE)
        content = re.sub(r'""".*?"""', '', content, flags=re.DOTALL)
        content = re.sub(r"'''.*?'''", '', content, flags=re.DOTALL)
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        return content.strip().lower()
    
    def _calculate_similarity_confidence(self, group: List[Dict]) -> float:
        """Calculate confidence score for a duplicate group"""
        if len(group) < 2:
            return 0.0
        
        total_similarity = 0.0
        comparisons = 0
        
        for i in range(len(group)):
            for j in range(i + 1, len(group)):
                content1 = group[i].get('content', '')
                content2 = group[j].get('content', '')
                similarity = self._calculate_content_similarity(content1, content2)
                total_similarity += similarity
                comparisons += 1
        
        return total_similarity / comparisons if comparisons > 0 else 0.0

class StructureAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze code structure"""
    
    def __init__(self):
        self.classes = {}
        self.functions = {}
        self.imports = {}
        self.current_class = None
        
    def visit_ClassDef(self, node):
        """Visit class definition"""
        class_info = {
            'name': node.name,
            'start_line': node.lineno,
            'end_line': getattr(node, 'end_lineno', node.lineno),
            'methods': [],
            'attributes': [],
            'content': ast.unparse(node) if hasattr(ast, 'unparse') else '',
            'docstring': ast.get_docstring(node)
        }
        
        self.classes[f"{node.name}_{node.lineno}"] = class_info
        
        # Visit methods within the class
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class
    
    def visit_FunctionDef(self, node):
        """Visit function definition"""
        func_info = {
            'name': node.name,
            'start_line': node.lineno,
            'end_line': getattr(node, 'end_lineno', node.lineno),
            'args': [arg.arg for arg in node.args.args],
            'content': ast.unparse(node) if hasattr(ast, 'unparse') else '',
            'docstring': ast.get_docstring(node),
            'class': self.current_class,
            'signature': self._get_function_signature(node)
        }
        
        key = f"{node.name}_{node.lineno}"
        if self.current_class:
            key = f"{self.current_class}.{key}"
            
        self.functions[key] = func_info
        self.generic_visit(node)
    
    def visit_Import(self, node):
        """Visit import statement"""
        for alias in node.names:
            import_info = {
                'module': alias.name,
                'alias': alias.asname,
                'line': node.lineno,
                'type': 'import',
                'content': f"import {alias.name}" + (f" as {alias.asname}" if alias.asname else "")
            }
            self.imports[f"import_{alias.name}_{node.lineno}"] = import_info
    
    def visit_ImportFrom(self, node):
        """Visit from...import statement"""
        module = node.module or ''
        for alias in node.names:
            import_info = {
                'module': f"{module}.{alias.name}" if module else alias.name,
                'alias': alias.asname,
                'line': node.lineno,
                'type': 'from_import',
                'from_module': module,
                'content': f"from {module} import {alias.name}" + (f" as {alias.asname}" if alias.asname else "")
            }
            self.imports[f"from_{module}_{alias.name}_{node.lineno}"] = import_info
    
    def _get_function_signature(self, node) -> str:
        """Get function signature string"""
        args = []
        for arg in node.args.args:
            args.append(arg.arg)
        
        return f"({', '.join(args)})"

class CodeCleaner:
    """Main code cleaner class"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.backup_path = f"{file_path}.backup"
        self.analyzer = CodeAnalyzer(file_path)
        self.cleaned_content = ""
        
    def clean_code(self) -> bool:
        """Main cleaning process"""
        logger.info(f"Starting code cleaning for {self.file_path}")
        
        # Create backup
        self._create_backup()
        
        # Load and analyze file
        if not self.analyzer.load_file():
            logger.error("Failed to load file")
            return False
        
        # Analyze structure
        structure = self.analyzer.analyze_structure()
        logger.info(f"File structure: {structure}")
        
        # Find duplicates
        duplicates = self.analyzer.find_duplicates()
        logger.info(f"Found {len(duplicates)} duplicate groups")
        
        # Remove duplicates
        self._remove_duplicates(duplicates)
        
        # Optimize imports
        self._optimize_imports()
        
        # Format code
        self._format_code()
        
        # Save cleaned file
        return self._save_cleaned_file()
    
    def _create_backup(self):
        """Create backup of original file"""
        try:
            import shutil
            shutil.copy2(self.file_path, self.backup_path)
            logger.info(f"Backup created: {self.backup_path}")
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
    
    def _remove_duplicates(self, duplicates: List[DuplicateGroup]):
        """Remove duplicate code blocks"""
        lines_to_remove = set()
        
        for duplicate_group in duplicates:
            if duplicate_group.confidence < 0.8:
                continue  # Skip low-confidence duplicates
            
            # Keep the first occurrence, remove others
            blocks_to_remove = duplicate_group.blocks[1:]
            
            for block in blocks_to_remove:
                for line_num in range(block.start_line, block.end_line + 1):
                    lines_to_remove.add(line_num - 1)  # Convert to 0-based indexing
                
                logger.info(f"Removing duplicate {block.block_type} '{block.name}' at lines {block.start_line}-{block.end_line}")
        
        # Remove lines
        cleaned_lines = []
        for i, line in enumerate(self.analyzer.lines):
            if i not in lines_to_remove:
                cleaned_lines.append(line)
        
        self.cleaned_content = '\n'.join(cleaned_lines)
    
    def _optimize_imports(self):
        """Optimize import statements"""
        # This would be implemented to remove unused imports
        # and organize them properly
        pass
    
    def _format_code(self):
        """Format the cleaned code"""
        # This would apply consistent formatting
        pass
    
    def _save_cleaned_file(self) -> bool:
        """Save the cleaned file"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.write(self.cleaned_content)
            logger.info(f"Cleaned file saved: {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save cleaned file: {e}")
            return False

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python code_cleaner.py <file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        sys.exit(1)
    
    cleaner = CodeCleaner(file_path)
    
    if cleaner.clean_code():
        print("✅ Code cleaning completed successfully!")
    else:
        print("❌ Code cleaning failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
